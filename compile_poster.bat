@echo off
echo ========================================
echo LaTeX Poster Compilation Script
echo ========================================
echo.

echo Compiling integrated_sustainability_poster.tex...
echo.

REM First compilation
echo [1/2] First pdflatex run...
pdflatex -interaction=nonstopmode integrated_sustainability_poster.tex

if %errorlevel% neq 0 (
    echo.
    echo ERROR: First compilation failed!
    echo Check the .log file for details.
    pause
    exit /b 1
)

REM Second compilation for cross-references
echo [2/2] Second pdflatex run...
pdflatex -interaction=nonstopmode integrated_sustainability_poster.tex

if %errorlevel% neq 0 (
    echo.
    echo ERROR: Second compilation failed!
    echo Check the .log file for details.
    pause
    exit /b 1
)

echo.
echo ========================================
echo SUCCESS: Poster compiled successfully!
echo ========================================
echo.
echo Output file: integrated_sustainability_poster.pdf
echo.

REM Clean up auxiliary files
echo Cleaning up auxiliary files...
del *.aux *.log *.fls *.fdb_latexmk *.figlist *.makefile *.figlist 2>nul

echo.
echo Compilation complete! 
echo You can now open integrated_sustainability_poster.pdf
echo.
pause
