#!/bin/bash

echo "========================================"
echo "LaTeX Poster Compilation Script"
echo "========================================"
echo

echo "Compiling integrated_sustainability_poster.tex..."
echo

# First compilation
echo "[1/2] First pdflatex run..."
pdflatex -interaction=nonstopmode integrated_sustainability_poster.tex

if [ $? -ne 0 ]; then
    echo
    echo "ERROR: First compilation failed!"
    echo "Check the .log file for details."
    exit 1
fi

# Second compilation for cross-references
echo "[2/2] Second pdflatex run..."
pdflatex -interaction=nonstopmode integrated_sustainability_poster.tex

if [ $? -ne 0 ]; then
    echo
    echo "ERROR: Second compilation failed!"
    echo "Check the .log file for details."
    exit 1
fi

echo
echo "========================================"
echo "SUCCESS: Poster compiled successfully!"
echo "========================================"
echo
echo "Output file: integrated_sustainability_poster.pdf"
echo

# Clean up auxiliary files
echo "Cleaning up auxiliary files..."
rm -f *.aux *.log *.fls *.fdb_latexmk *.figlist *.makefile 2>/dev/null

echo
echo "Compilation complete!"
echo "You can now open integrated_sustainability_poster.pdf"
echo

# Make the script executable
chmod +x compile_poster.sh
