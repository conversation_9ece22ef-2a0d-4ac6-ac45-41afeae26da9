# LaTeX Poster Compilation Guide

## Overview
This directory contains a professional LaTeX poster presentation for the research paper "Beyond Silos: An Integrated Sustainability Governance Framework for Multi-Dimensional Sustainable Development Goals Implementation."

## Files Included
- `integrated_sustainability_poster.tex` - Main LaTeX poster document
- `media/image1.png` - PRISMA Flow Diagram (referenced in original paper)
- `media/image2.png` - Theoretical Foundations diagram (referenced in original paper)
- `media/image3.png` - ISGF Framework diagram (referenced in original paper)

## Required LaTeX Packages
The poster uses the following LaTeX packages (most are included in standard LaTeX distributions):

### Core Packages
- `tikzposter` - Main poster creation package
- `inputenc` - UTF-8 input encoding
- `fontenc` - Font encoding
- `lmodern` - Latin Modern fonts
- `amsmath`, `amsfonts`, `amssymb` - Mathematical symbols
- `graphicx` - Image inclusion
- `booktabs` - Professional table formatting
- `array` - Enhanced array and tabular environments
- `multirow` - Multi-row table cells
- `xcolor` - Color support
- `tikz` - Graphics and drawing
- `fontawesome5` - FontAwesome icons
- `url` - URL formatting

### Installation Notes
- **TeX Live/MiKTeX**: Most packages are included by default
- **Overleaf**: All packages are available online
- **Manual Installation**: If missing packages, install via your TeX distribution's package manager

## Compilation Instructions

### Method 1: Command Line
```bash
# Navigate to the directory containing the .tex file
cd /path/to/poster/directory

# Compile with pdflatex (recommended)
pdflatex integrated_sustainability_poster.tex

# For better bibliography handling (if needed)
pdflatex integrated_sustainability_poster.tex
pdflatex integrated_sustainability_poster.tex
```

### Method 2: LaTeX Editor
- **TeXstudio/TeXmaker**: Open the .tex file and press F5 or use Build menu
- **Overleaf**: Upload all files and click "Recompile"
- **VS Code with LaTeX Workshop**: Use Ctrl+Alt+B

### Method 3: Online Compilation
1. Upload all files to Overleaf (overleaf.com)
2. Set compiler to "pdfLaTeX"
3. Click "Recompile"

## Output Specifications
- **Paper Size**: A0 (841 × 1189 mm)
- **Orientation**: Portrait
- **Margins**: 15mm inner margin
- **Font Size**: 25pt base size (automatically scaled for poster)
- **Layout**: Three-column design with bottom section
- **Color Scheme**: Professional blue-gray-orange palette

## Image Requirements
Ensure the following image files are in the `media/` subdirectory:
- `image1.png` - PRISMA flow diagram
- `image2.png` - Theoretical foundations
- `image3.png` - ISGF framework

**Image Specifications for A0 Printing:**
- Resolution: Minimum 300 DPI
- Format: PNG, PDF, or EPS preferred
- Size: Images will be automatically scaled to fit poster layout

## Customization Options

### Color Scheme
The poster uses a professional color palette defined in the preamble:
```latex
\definecolor{primaryblue}{RGB}{0,102,153}
\definecolor{secondaryblue}{RGB}{51,153,204}
\definecolor{accentgreen}{RGB}{0,128,96}
```

### Theme Options
Current theme: `Rays` with `BlueGrayOrange` color style
Alternative themes available:
- `Default`, `Basic`, `Simple`, `Envelope`, `Wave`, `Board`, `Autumn`

### Font Size Adjustment
Base font size is set to 25pt. For different sizes, modify the document class:
```latex
\documentclass[20pt, a0paper, ...]{tikzposter}  % Smaller text
\documentclass[30pt, a0paper, ...]{tikzposter}  % Larger text
```

## Troubleshooting

### Common Issues
1. **Missing packages**: Install via TeX package manager
2. **Image not found**: Check file paths in `media/` directory
3. **Font issues**: Ensure `fontawesome5` package is installed
4. **Compilation errors**: Check LaTeX log file for specific errors

### FontAwesome Icons
If FontAwesome icons don't display:
1. Install `fontawesome5` package
2. Alternative: Replace icon commands with text symbols
3. Use `\usepackage{fontawesome}` for older distributions

### Memory Issues
For large posters, increase TeX memory:
```bash
# Increase memory limits
export max_print_line=1000
export error_line=254
export half_error_line=238
```

## Professional Printing Tips
- **Print Shop Requirements**: Provide PDF output, specify A0 size
- **Color Profile**: Use CMYK color profile for professional printing
- **Bleed**: Add 3-5mm bleed if required by print shop
- **Resolution**: Ensure all images are 300 DPI minimum
- **Proofing**: Print A4 version first to check layout and colors

## Support
For LaTeX-specific issues:
- TeX Stack Exchange: tex.stackexchange.com
- TikZ/PGF Manual: Available in CTAN documentation
- tikzposter Documentation: CTAN package documentation

This poster is ready for professional academic conference presentation and A0 printing.
