# Makefile for LaTeX Poster Compilation
# Usage: make poster

# Variables
TEX_FILE = integrated_sustainability_poster.tex
PDF_FILE = integrated_sustainability_poster.pdf
LATEX_CMD = pdflatex
LATEX_FLAGS = -interaction=nonstopmode

# Default target
.PHONY: all
all: poster

# Main poster compilation target
.PHONY: poster
poster: $(PDF_FILE)

$(PDF_FILE): $(TEX_FILE)
	@echo "========================================="
	@echo "Compiling LaTeX Poster..."
	@echo "========================================="
	@echo "[1/2] First pdflatex run..."
	$(LATEX_CMD) $(LATEX_FLAGS) $(TEX_FILE)
	@echo "[2/2] Second pdflatex run..."
	$(LATEX_CMD) $(LATEX_FLAGS) $(TEX_FILE)
	@echo "========================================="
	@echo "SUCCESS: Poster compiled successfully!"
	@echo "Output: $(PDF_FILE)"
	@echo "========================================="

# Clean auxiliary files
.PHONY: clean
clean:
	@echo "Cleaning auxiliary files..."
	rm -f *.aux *.log *.fls *.fdb_latexmk *.figlist *.makefile *.out *.toc *.nav *.snm

# Clean everything including PDF
.PHONY: cleanall
cleanall: clean
	@echo "Removing PDF output..."
	rm -f $(PDF_FILE)

# Quick compilation (single pass)
.PHONY: quick
quick:
	@echo "Quick compilation (single pass)..."
	$(LATEX_CMD) $(LATEX_FLAGS) $(TEX_FILE)

# View PDF (works on systems with default PDF viewer)
.PHONY: view
view: $(PDF_FILE)
	@echo "Opening PDF..."
	@if command -v xdg-open > /dev/null; then \
		xdg-open $(PDF_FILE); \
	elif command -v open > /dev/null; then \
		open $(PDF_FILE); \
	elif command -v start > /dev/null; then \
		start $(PDF_FILE); \
	else \
		echo "Please open $(PDF_FILE) manually"; \
	fi

# Help target
.PHONY: help
help:
	@echo "Available targets:"
	@echo "  poster    - Compile the poster (default)"
	@echo "  clean     - Remove auxiliary files"
	@echo "  cleanall  - Remove all generated files"
	@echo "  quick     - Quick single-pass compilation"
	@echo "  view      - Open the compiled PDF"
	@echo "  help      - Show this help message"

# Dependencies check
.PHONY: check
check:
	@echo "Checking LaTeX installation..."
	@command -v pdflatex >/dev/null 2>&1 || { echo "pdflatex not found. Please install LaTeX."; exit 1; }
	@echo "pdflatex found: $$(which pdflatex)"
	@echo "Checking required image files..."
	@test -f media/image1.png || echo "WARNING: media/image1.png not found"
	@test -f media/image2.png || echo "WARNING: media/image2.png not found"
	@test -f media/image3.png || echo "WARNING: media/image3.png not found"
	@echo "Dependency check complete."
